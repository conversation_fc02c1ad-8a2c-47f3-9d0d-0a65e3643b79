/* CSS Custom Properties for Theming */
:root {
    /* Light Theme Colors */
    --primary-color: #1e3a8a;
    --secondary-color: #f59e0b;
    --accent-color: #3b82f6;
    --text-primary: #1f2937;
    --text-secondary: #6b7280;
    --background-primary: #ffffff;
    --background-secondary: #f8fafc;
    --background-tertiary: #e2e8f0;
    --border-color: #e5e7eb;
    --shadow-light: 0 1px 3px rgba(0, 0, 0, 0.1);
    --shadow-medium: 0 4px 6px rgba(0, 0, 0, 0.1);
    --shadow-heavy: 0 10px 25px rgba(0, 0, 0, 0.15);
    --gradient-primary: linear-gradient(135deg, #1e3a8a 0%, #3b82f6 100%);
    --gradient-secondary: linear-gradient(135deg, #f59e0b 0%, #fbbf24 100%);
}

/* Dark Theme Colors */
[data-theme="dark"] {
    --primary-color: #3b82f6;
    --secondary-color: #fbbf24;
    --accent-color: #60a5fa;
    --text-primary: #f9fafb;
    --text-secondary: #d1d5db;
    --background-primary: #111827;
    --background-secondary: #1f2937;
    --background-tertiary: #374151;
    --border-color: #4b5563;
    --shadow-light: 0 1px 3px rgba(0, 0, 0, 0.3);
    --shadow-medium: 0 4px 6px rgba(0, 0, 0, 0.3);
    --shadow-heavy: 0 10px 25px rgba(0, 0, 0, 0.4);
}

/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

html {
    scroll-behavior: smooth;
    font-size: 16px;
}

body {
    font-family: 'Poppins', sans-serif;
    line-height: 1.6;
    color: var(--text-primary);
    background-color: var(--background-primary);
    transition: all 0.3s ease;
    overflow-x: hidden;
}

/* Loading Screen */
#loading-screen {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: var(--gradient-primary);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 9999;
    transition: opacity 0.5s ease;
}

.loading-spinner {
    text-align: center;
    color: white;
}

.spinner {
    width: 50px;
    height: 50px;
    border: 4px solid rgba(255, 255, 255, 0.3);
    border-top: 4px solid white;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin: 0 auto 20px;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Navigation */
.navbar {
    position: fixed;
    top: 0;
    width: 100%;
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    border-bottom: 1px solid var(--border-color);
    z-index: 1000;
    transition: all 0.3s ease;
}

[data-theme="dark"] .navbar {
    background: rgba(17, 24, 39, 0.95);
}

.nav-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    height: 70px;
}

.nav-logo h2 {
    color: var(--primary-color);
    font-weight: 700;
    font-size: 1.5rem;
}

.nav-menu {
    display: flex;
    list-style: none;
    align-items: center;
    gap: 2rem;
}

.nav-link {
    text-decoration: none;
    color: var(--text-primary);
    font-weight: 500;
    position: relative;
    transition: color 0.3s ease;
}

.nav-link:hover,
.nav-link.active {
    color: var(--primary-color);
}

.nav-link::after {
    content: '';
    position: absolute;
    bottom: -5px;
    left: 0;
    width: 0;
    height: 2px;
    background: var(--primary-color);
    transition: width 0.3s ease;
}

.nav-link:hover::after,
.nav-link.active::after {
    width: 100%;
}

.theme-toggle {
    background: none;
    border: 2px solid var(--primary-color);
    color: var(--primary-color);
    padding: 8px 12px;
    border-radius: 50px;
    cursor: pointer;
    transition: all 0.3s ease;
}

.theme-toggle:hover {
    background: var(--primary-color);
    color: white;
}

.hamburger {
    display: none;
    flex-direction: column;
    cursor: pointer;
}

.bar {
    width: 25px;
    height: 3px;
    background: var(--text-primary);
    margin: 3px 0;
    transition: 0.3s;
}

/* Hero Section */
.hero {
    height: 100vh;
    position: relative;
    display: flex;
    align-items: center;
    justify-content: center;
    overflow: hidden;
}

.hero-background {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: var(--gradient-primary);
    background-image: 
        radial-gradient(circle at 20% 80%, rgba(120, 119, 198, 0.3) 0%, transparent 50%),
        radial-gradient(circle at 80% 20%, rgba(255, 255, 255, 0.1) 0%, transparent 50%);
    z-index: -1;
}

.hero-content {
    text-align: center;
    color: white;
    max-width: 800px;
    padding: 0 20px;
    z-index: 1;
}

.hero-title {
    font-size: clamp(2.5rem, 5vw, 4rem);
    font-weight: 700;
    margin-bottom: 1rem;
    font-family: 'Merriweather', serif;
}

.typing-text {
    display: inline-block;
}

.cursor {
    animation: blink 1s infinite;
}

@keyframes blink {
    0%, 50% { opacity: 1; }
    51%, 100% { opacity: 0; }
}

.hero-subtitle {
    font-size: 1.25rem;
    margin-bottom: 2rem;
    opacity: 0.9;
}

.hero-stats {
    display: flex;
    justify-content: center;
    gap: 3rem;
    margin-bottom: 3rem;
}

.stat {
    text-align: center;
}

.stat-number {
    display: block;
    font-size: 2.5rem;
    font-weight: 700;
    color: var(--secondary-color);
}

.stat-label {
    font-size: 0.9rem;
    opacity: 0.8;
}

.hero-buttons {
    display: flex;
    gap: 1rem;
    justify-content: center;
    flex-wrap: wrap;
}

.btn {
    display: inline-block;
    padding: 12px 30px;
    border-radius: 50px;
    text-decoration: none;
    font-weight: 600;
    transition: all 0.3s ease;
    border: 2px solid transparent;
    cursor: pointer;
    font-size: 1rem;
}

.btn-primary {
    background: var(--secondary-color);
    color: white;
    border-color: var(--secondary-color);
}

.btn-primary:hover {
    background: transparent;
    color: var(--secondary-color);
    border-color: var(--secondary-color);
    transform: translateY(-2px);
    box-shadow: var(--shadow-medium);
}

.btn-secondary {
    background: transparent;
    color: white;
    border-color: white;
}

.btn-secondary:hover {
    background: white;
    color: var(--primary-color);
    transform: translateY(-2px);
}

.scroll-indicator {
    position: absolute;
    bottom: 30px;
    left: 50%;
    transform: translateX(-50%);
}

.scroll-arrow {
    width: 30px;
    height: 30px;
    border: 2px solid white;
    border-top: none;
    border-left: none;
    transform: rotate(45deg);
    animation: bounce 2s infinite;
}

@keyframes bounce {
    0%, 20%, 50%, 80%, 100% { transform: translateX(-50%) rotate(45deg) translateY(0); }
    40% { transform: translateX(-50%) rotate(45deg) translateY(-10px); }
    60% { transform: translateX(-50%) rotate(45deg) translateY(-5px); }
}

/* Common Section Styles */
.section {
    padding: 80px 0;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

.section-header {
    text-align: center;
    margin-bottom: 60px;
}

.section-title {
    font-size: clamp(2rem, 4vw, 3rem);
    font-weight: 700;
    color: var(--text-primary);
    margin-bottom: 1rem;
    font-family: 'Merriweather', serif;
}

.section-subtitle {
    font-size: 1.1rem;
    color: var(--text-secondary);
    max-width: 600px;
    margin: 0 auto;
}

/* Animation Classes */
.fade-in-up {
    opacity: 0;
    transform: translateY(30px);
    animation: fadeInUp 0.8s ease forwards;
}

@keyframes fadeInUp {
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.fade-in {
    opacity: 0;
    animation: fadeIn 0.8s ease forwards;
}

@keyframes fadeIn {
    to { opacity: 1; }
}

/* About Section */
.about {
    background: var(--background-secondary);
}

.about-content {
    display: grid;
    grid-template-columns: 2fr 1fr;
    gap: 4rem;
    align-items: center;
}

.about-card {
    background: var(--background-primary);
    padding: 2rem;
    border-radius: 15px;
    box-shadow: var(--shadow-light);
    margin-bottom: 2rem;
    transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.about-card:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-medium);
}

.about-card h3 {
    color: var(--primary-color);
    margin-bottom: 1rem;
    font-size: 1.25rem;
}

.about-image {
    display: flex;
    justify-content: center;
    align-items: center;
}

.image-placeholder {
    width: 300px;
    height: 300px;
    background: var(--gradient-primary);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 6rem;
    color: white;
    box-shadow: var(--shadow-heavy);
}

/* Services Section */
.services-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 2rem;
}

.service-card {
    background: var(--background-primary);
    padding: 2.5rem 2rem;
    border-radius: 15px;
    text-align: center;
    box-shadow: var(--shadow-light);
    transition: all 0.3s ease;
    border: 1px solid var(--border-color);
}

.service-card:hover {
    transform: translateY(-10px);
    box-shadow: var(--shadow-heavy);
    border-color: var(--primary-color);
}

.service-icon {
    width: 80px;
    height: 80px;
    background: var(--gradient-primary);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 1.5rem;
    font-size: 2rem;
    color: white;
    transition: transform 0.3s ease;
}

.service-card:hover .service-icon {
    transform: scale(1.1);
}

.service-card h3 {
    color: var(--text-primary);
    margin-bottom: 1rem;
    font-size: 1.25rem;
}

.service-card p {
    color: var(--text-secondary);
    line-height: 1.6;
}

/* Testimonials Section */
.testimonials {
    background: var(--background-secondary);
}

.testimonials-container {
    position: relative;
    max-width: 800px;
    margin: 0 auto;
    overflow: hidden;
}

.testimonial-card {
    display: none;
    background: var(--background-primary);
    padding: 3rem 2rem;
    border-radius: 15px;
    box-shadow: var(--shadow-medium);
    text-align: center;
    animation: slideIn 0.5s ease;
}

.testimonial-card.active {
    display: block;
}

@keyframes slideIn {
    from {
        opacity: 0;
        transform: translateX(30px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

.quote-icon {
    font-size: 3rem;
    color: var(--secondary-color);
    margin-bottom: 1rem;
}

.testimonial-content p {
    font-size: 1.1rem;
    color: var(--text-secondary);
    margin-bottom: 2rem;
    font-style: italic;
    line-height: 1.8;
}

.client-info h4 {
    color: var(--text-primary);
    margin-bottom: 0.5rem;
}

.client-info span {
    color: var(--text-secondary);
    font-size: 0.9rem;
}

.testimonial-nav {
    display: flex;
    justify-content: center;
    gap: 1rem;
    margin-top: 2rem;
}

.nav-dot {
    width: 12px;
    height: 12px;
    border-radius: 50%;
    border: none;
    background: var(--border-color);
    cursor: pointer;
    transition: background 0.3s ease;
}

.nav-dot.active {
    background: var(--primary-color);
}

/* Contact Section */
.contact-content {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 4rem;
}

.contact-item {
    display: flex;
    align-items: flex-start;
    gap: 1rem;
    margin-bottom: 2rem;
}

.contact-icon {
    width: 50px;
    height: 50px;
    background: var(--gradient-primary);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 1.2rem;
    flex-shrink: 0;
}

.contact-details h4 {
    color: var(--text-primary);
    margin-bottom: 0.5rem;
}

.contact-details p {
    color: var(--text-secondary);
    line-height: 1.6;
}

/* Contact Form */
.contact-form {
    background: var(--background-secondary);
    padding: 2.5rem;
    border-radius: 15px;
    box-shadow: var(--shadow-light);
}

.form-group {
    position: relative;
    margin-bottom: 2rem;
}

.form-group input,
.form-group select,
.form-group textarea {
    width: 100%;
    padding: 15px;
    border: 2px solid var(--border-color);
    border-radius: 10px;
    background: var(--background-primary);
    color: var(--text-primary);
    font-size: 1rem;
    transition: border-color 0.3s ease;
}

.form-group input:focus,
.form-group select:focus,
.form-group textarea:focus {
    outline: none;
    border-color: var(--primary-color);
}

.form-group label {
    position: absolute;
    top: 15px;
    left: 15px;
    color: var(--text-secondary);
    transition: all 0.3s ease;
    pointer-events: none;
}

.form-group input:focus + label,
.form-group input:valid + label,
.form-group select:focus + label,
.form-group select:valid + label,
.form-group textarea:focus + label,
.form-group textarea:valid + label {
    top: -10px;
    left: 10px;
    font-size: 0.8rem;
    color: var(--primary-color);
    background: var(--background-secondary);
    padding: 0 5px;
}

.error-message {
    color: #ef4444;
    font-size: 0.8rem;
    margin-top: 0.5rem;
    display: block;
}

/* Footer */
.footer {
    background: var(--background-tertiary);
    padding: 3rem 0 1rem;
}

.footer-content {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 2rem;
    margin-bottom: 2rem;
}

.footer-section h3,
.footer-section h4 {
    color: var(--text-primary);
    margin-bottom: 1rem;
}

.footer-section p,
.footer-section li {
    color: var(--text-secondary);
    margin-bottom: 0.5rem;
}

.footer-section ul {
    list-style: none;
}

.footer-section a {
    color: var(--text-secondary);
    text-decoration: none;
    transition: color 0.3s ease;
}

.footer-section a:hover {
    color: var(--primary-color);
}

.social-links {
    display: flex;
    gap: 1rem;
    margin-top: 1rem;
}

.social-link {
    width: 40px;
    height: 40px;
    background: var(--primary-color);
    color: white;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: transform 0.3s ease;
}

.social-link:hover {
    transform: translateY(-3px);
    background: var(--secondary-color);
}

.footer-bottom {
    text-align: center;
    padding-top: 2rem;
    border-top: 1px solid var(--border-color);
    color: var(--text-secondary);
}

/* Scroll to Top Button */
.scroll-to-top {
    position: fixed;
    bottom: 30px;
    right: 30px;
    width: 50px;
    height: 50px;
    background: var(--primary-color);
    color: white;
    border: none;
    border-radius: 50%;
    cursor: pointer;
    opacity: 0;
    visibility: hidden;
    transition: all 0.3s ease;
    z-index: 1000;
}

.scroll-to-top.visible {
    opacity: 1;
    visibility: visible;
}

.scroll-to-top:hover {
    background: var(--secondary-color);
    transform: translateY(-3px);
}

/* Modal */
.modal {
    display: none;
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    z-index: 2000;
    justify-content: center;
    align-items: center;
}

.modal.active {
    display: flex;
}

.modal-content {
    background: var(--background-primary);
    padding: 2rem;
    border-radius: 15px;
    text-align: center;
    max-width: 400px;
    margin: 0 20px;
    box-shadow: var(--shadow-heavy);
}

.modal-header {
    margin-bottom: 1rem;
}

.modal-header i {
    font-size: 3rem;
    color: #10b981;
    margin-bottom: 1rem;
}

.modal-header h3 {
    color: var(--text-primary);
    margin-bottom: 1rem;
}

.modal-content p {
    color: var(--text-secondary);
    margin-bottom: 2rem;
}

/* Responsive Design */
@media (max-width: 768px) {
    .hamburger {
        display: flex;
    }
    
    .nav-menu {
        position: fixed;
        left: -100%;
        top: 70px;
        flex-direction: column;
        background: var(--background-primary);
        width: 100%;
        text-align: center;
        transition: 0.3s;
        box-shadow: var(--shadow-medium);
        padding: 2rem 0;
    }
    
    .nav-menu.active {
        left: 0;
    }
    
    .nav-menu li {
        margin: 1rem 0;
    }
    
    .hero-stats {
        flex-direction: column;
        gap: 1.5rem;
    }
    
    .hero-buttons {
        flex-direction: column;
        align-items: center;
    }
    
    .about-content {
        grid-template-columns: 1fr;
        text-align: center;
    }
    
    .contact-content {
        grid-template-columns: 1fr;
    }
    
    .services-grid {
        grid-template-columns: 1fr;
    }
    
    .image-placeholder {
        width: 200px;
        height: 200px;
        font-size: 4rem;
    }
}

@media (max-width: 480px) {
    .container {
        padding: 0 15px;
    }
    
    .hero-content {
        padding: 0 15px;
    }
    
    .section {
        padding: 60px 0;
    }
    
    .contact-form {
        padding: 1.5rem;
    }
    
    .footer-content {
        grid-template-columns: 1fr;
        text-align: center;
    }
}
